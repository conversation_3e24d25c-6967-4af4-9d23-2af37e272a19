<script lang="ts">
	import type { PageData } from './$types';
	import { Button } from '$lib/components/ui/button';
	import { superForm } from 'sveltekit-superforms';
	import WbsTreeItem from '$lib/components/wbs-tree-item.svelte';

	// Import shadcn components
	import { Input } from '$lib/components/ui/input';
	import { Textarea } from '$lib/components/ui/textarea';
	import * as Form from '$lib/components/ui/form';
	import * as Popover from '$lib/components/ui/popover';
	import * as Command from '$lib/components/ui/command';
	import { toast } from 'svelte-sonner';
	import CheckIcon from 'phosphor-svelte/lib/Check';
	import CaretUpDownIcon from 'phosphor-svelte/lib/CaretUpDown';
	import { buttonVariants } from '$lib/components/ui/button';
	import { cn } from '$lib/utils';
	import { tick } from 'svelte';
	import { useId } from 'bits-ui';

	const { data }: { data: PageData } = $props();
	const form = superForm(data.form, {
		onUpdated({ form }) {
			if (form.message) {
				if (form.message.type === 'success') {
					toast.success(form.message.text);
				} else if (form.message.type === 'error') {
					toast.error(form.message.text);
				}
			}
		},
	});
	const { form: formData, enhance: superEnhance, reset } = form;

	let showAddForm = $state(false);
	let expandedClientCategories = $state<Record<number, boolean>>({});
	// let expandedMainCategories = $state<Record<number, boolean>>({});
	let openParentCombobox = $state(false);
	const triggerId = useId();

	// Check if any items have scope defined to show the column
	const showClientScopeColumn = $derived(data.clientItems.some((item) => item.cost_scope));
	// const showMainScopeColumn = $derived(data.mainWbsLibraryItems.some((item) => item.cost_scope));

	// Create a combined list of parent options
	const parentOptions = $derived.by(() => {
		const options: {
			label: string;
			value: string;
			source: 'client' | 'project' | 'main';
			level: number;
		}[] = [];

		// Add client items as potential parents
		data.clientItems.forEach((item) => {
			options.push({
				label: `${item.code}: ${item.description}`,
				value: String(item.wbs_library_item_id),
				source: 'client',
				level: item.level || 1,
			});
		});

		// Add main WBS library items as potential parents
		data.mainWbsLibraryItems.forEach((item) => {
			if ($formData.wbs_library_id && $formData.wbs_library_id !== item.wbs_library_id) return;

			options.push({
				label: `${item.code}: ${item.description}`,
				value: String(item.wbs_library_item_id),
				source: 'main',
				level: item.level || 1,
			});
		});

		// TODO: figure out a system for filtering based on level

		return options;
	});

	function getParentName(parentId: string | null | undefined): string {
		if (!parentId) return 'None';

		const clientItem = data.clientItems.find((item) => item.wbs_library_item_id === parentId);
		if (clientItem) {
			return `${clientItem.code}: ${clientItem.description}`;
		}

		const mainItem = data.mainWbsLibraryItems.find((item) => item.wbs_library_item_id === parentId);
		if (mainItem) {
			return `${mainItem.code}: ${mainItem.description}`;
		}

		return 'Unknown';
	}

	function startAdd() {
		showAddForm = true;
		reset();

		// Pre-fill client ID and project ID
		$formData.client_id = data.client.client_id;
		$formData.level = 1;
	}

	// TODO: link level and parent item selection

	function cancelAdd() {
		showAddForm = false;
		reset();
	}

	let triggerRef = $state<HTMLButtonElement>(null!);
	function closeAndFocusTrigger() {
		openParentCombobox = false;
		tick().then(() => {
			triggerRef?.focus();
		});
	}

	function getLibraryName(libraryId: number): string {
		const library = data.wbsLibraries.find((lib) => lib.wbs_library_id === libraryId);
		return library ? library.name : 'Unknown Library';
	}
</script>

<div class="container max-w-(--breakpoint-xl) py-8">
	<div class="mb-4 flex items-center justify-between">
		<div>
			<h1 class="text-3xl font-bold">{data.client.name} - Work Breakdown Structure</h1>
			<p class="mt-1 text-slate-600">
				Manage client-specific WBS items that will be available to all projects
			</p>
		</div>
	</div>

	<div class="mb-6 flex justify-end">
		{#if !showAddForm}
			<Button onclick={startAdd}>Add Custom WBS Item</Button>
		{/if}
	</div>

	{#if showAddForm}
		<div class="mb-8 rounded-lg border bg-white p-6">
			<h2 class="mb-4 text-xl font-semibold">Add a Client-specific WBS Item</h2>

			<form method="POST" action="?/createItem" class="space-y-6" use:superEnhance>
				<input type="hidden" name="client_id" value={$formData.client_id} />

				<Form.Field {form} name="wbs_library_id">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>WBS Library</Form.Label>
							<select
								{...props}
								bind:value={$formData.wbs_library_id}
								class="border-input bg-background ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-hidden"
							>
								<option selected disabled hidden value={0}>Select a WBS library</option>
								{#each data.wbsLibraries as library (library.wbs_library_id)}
									<option value={library.wbs_library_id}>{library.name}</option>
								{/each}
							</select>
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
					<Form.Field {form} name="level">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Level</Form.Label>
								<Input {...props} type="number" value={$formData.level} min="1" />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>

					<Form.Field {form} name="parent_item_id">
						<Popover.Root bind:open={openParentCombobox}>
							<Form.Control id={triggerId}>
								{#snippet children({ props })}
									<Form.Label>Parent Item</Form.Label>
									<Popover.Trigger
										class={cn(
											buttonVariants({ variant: 'outline' }),
											'w-full justify-between',
											!$formData.parent_item_id && 'text-muted-foreground',
										)}
										role="combobox"
										bind:ref={triggerRef}
										{...props}
									>
										{$formData.parent_item_id
											? getParentName($formData.parent_item_id)
											: 'Select parent item'}
										<CaretUpDownIcon class="ml-2 size-4 shrink-0 opacity-50" />
									</Popover.Trigger>
									<input hidden value={$formData.parent_item_id} name={props.name} />
								{/snippet}
							</Form.Control>
							<Popover.Content class="w-[400px] p-0">
								<Command.Root>
									<Command.Input autofocus placeholder="Search parent items..." class="h-9" />
									<Command.Empty>No matching items found.</Command.Empty>
									<Command.Group class="max-h-[300px] overflow-y-auto">
										{#each parentOptions as option (option.value)}
											<Command.Item
												value={option.label}
												onSelect={() => {
													$formData.parent_item_id = option.value;
													closeAndFocusTrigger();
												}}
											>
												<span
													class={option.source === 'client'
														? 'text-blue-600'
														: option.source === 'main'
															? 'text-green-600'
															: ''}
												>
													{option.label}
												</span>
												<CheckIcon
													class={cn(
														'ml-auto size-4',
														option.value !== String($formData.parent_item_id) && 'text-transparent',
													)}
												/>
											</Command.Item>
										{/each}
									</Command.Group>
								</Command.Root>
							</Popover.Content>
						</Popover.Root>
						<Form.Description>
							{#if $formData.level > 1}
								Showing only level {$formData.level - 1} items as possible parents. Client items in blue,
								library items in green.
							{:else}
								{$formData.wbs_library_id
									? getLibraryName($formData.wbs_library_id) +
										' items in green and client-wide items are shown in blue.'
									: ''}
							{/if}
						</Form.Description>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
					<Form.Field {form} name="code">
						<Form.Control>
							{#snippet children({ props })}
								<Form.Label>Code</Form.Label>
								<Input {...props} value={$formData.code ?? ''} />
							{/snippet}
						</Form.Control>
						<Form.FieldErrors />
					</Form.Field>
				</div>

				<Form.Field {form} name="description">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Description</Form.Label>
							<Input {...props} value={$formData.description ?? ''} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<Form.Field {form} name="cost_scope">
					<Form.Control>
						{#snippet children({ props })}
							<Form.Label>Scope Details</Form.Label>
							<Textarea {...props} value={$formData.cost_scope ?? ''} rows={4} />
						{/snippet}
					</Form.Control>
					<Form.FieldErrors />
				</Form.Field>

				<div class="flex justify-end space-x-2 pt-4">
					<Button type="button" variant="outline" onclick={cancelAdd}>Cancel</Button>
					<Form.Button>Save WBS Item</Form.Button>
				</div>
			</form>
		</div>
	{/if}

	{#if data.clientItems.length === 0}
		<div class="rounded-lg border bg-slate-50 p-8 text-center">
			<h2 class="mb-2 text-xl font-semibold">No Custom WBS Items</h2>
			<p class="mb-6 text-slate-600">This client doesn't have any custom WBS items yet.</p>
		</div>
	{:else}
		<div class="rounded-lg border bg-white p-6">
			<h2 class="mb-4 text-xl font-semibold">Client WBS Structure</h2>

			<div class="overflow-x-auto">
				<table class="min-w-full divide-y divide-gray-200">
					<thead class="bg-gray-50">
						<tr>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Code
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Description
							</th>
							{#if showClientScopeColumn}
								<th
									scope="col"
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
								>
									Scope Details
								</th>
							{/if}
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Library
							</th>
							<th
								scope="col"
								class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
							>
								Actions
							</th>
						</tr>
					</thead>
					<tbody class="divide-y divide-gray-200 bg-white">
						{#each data.clientItemsTree as item (item.wbs_library_item_id)}
							<WbsTreeItem
								{item}
								depth={0}
								showScope={showClientScopeColumn}
								expandedCategories={expandedClientCategories}
							>
								{#snippet additionalColumns()}
									<td class="px-6 py-4 text-sm whitespace-nowrap">
										{getLibraryName(item.wbs_library_id)}
									</td>
								{/snippet}
							</WbsTreeItem>
						{/each}
					</tbody>
				</table>
			</div>
		</div>
	{/if}
</div>
